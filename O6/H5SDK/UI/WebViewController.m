
//  WebViewController.m
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import "WebViewController.h"
#import "GlobalDataManager.h"
#import "DeviceInfoManager.h"
#import "RiskControlManager.h"
#import "PaymentManager.h"
#import "APIClient.h"
#import "Constants.h"
#import <PhotosUI/PhotosUI.h>

// 弱引用消息处理器代理类，防止循环引用
@interface WeakScriptMessageHandler : NSObject <WKScriptMessageHandler>

@property (nonatomic, weak) id<WKScriptMessageHandler> delegate;

- (instancetype)initWithDelegate:(id<WKScriptMessageHandler>)delegate;

@end

@implementation WeakScriptMessageHandler

- (instancetype)initWithDelegate:(id<WKScriptMessageHandler>)delegate {
    if (self = [super init]) {
        self.delegate = delegate;
    }
    return self;
}

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    if (self.delegate) {
        [self.delegate userContentController:userContentController didReceiveScriptMessage:message];
    }
}

@end

@interface WebViewController ()

@property (nonatomic, strong) BaseMiniView *webView;
@property (nonatomic, strong) UIProgressView *progressView;
@property (nonatomic, strong) WKWebViewConfiguration *webConfiguration;
@property (nonatomic, strong) NSString *initialURL;
@property (nonatomic, strong) NSDictionary *initialParameters;
@property (nonatomic, strong) GlobalDataManager *globalDataManager;
@property (nonatomic, strong) DeviceInfoManager *deviceInfoManager;
@property (nonatomic, strong) RiskControlManager *riskControlMananer;
@property (nonatomic, strong) PaymentManager *paymentManager;
@property (nonatomic, strong) NSArray *registeredMessageHandlers;
@property (nonatomic, assign) BOOL isProtocolContentMode;
@property (nonatomic, strong) NSMutableDictionary<NSString *, WeakScriptMessageHandler *> *messageHandlerProxies;

@end

@implementation WebViewController

#pragma mark - 初始化方法

- (instancetype)initWithURL:(NSString *)url {
    return [self initWithURL:url initialParameters:nil];
}

- (instancetype)initWithURL:(NSString *)url initialParameters:(nullable NSDictionary *)parameters {
    if (self = [super init]) {
        self.initialURL = url;
        self.initialParameters = parameters;
        self.isProtocolContentMode = NO;
        self.messageHandlerProxies = [NSMutableDictionary dictionary];
        self.globalDataManager = [GlobalDataManager sharedInstance];
        self.deviceInfoManager = [DeviceInfoManager sharedInstance];
        self.riskControlMananer = [RiskControlManager sharedInstance];
        self.paymentManager = [PaymentManager sharedInstance];
        [self setupWebViewConfiguration];
    }
    return self;
}

- (instancetype)initWithURLForProtocolContent:(NSString *)url {
    if (self = [super init]) {
        self.initialURL = url;
        self.initialParameters = nil;
        self.isProtocolContentMode = YES;
        self.messageHandlerProxies = [NSMutableDictionary dictionary];
        // 协议内容模式下不需要初始化这些管理器
        [self setupWebViewConfiguration];
    }
    return self;
}

#pragma mark - 生命周期

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self setupWebView];
    [self setupProgressView];
    [self registerDefaultMessageHandlers];
    [self setupKeyboardNotifications];
    [self setupAppLifecycleNotifications];

    // 加载初始URL
    if (self.initialURL) {
        [self loadURL:self.initialURL withParameters:self.initialParameters];
    }
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:!self.isProtocolContentMode animated:animated];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    // 移除KVO观察
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    // 移除键盘通知监听
    [self removeKeyboardNotifications];
    // 移除应用生命周期通知监听
    [self removeAppLifecycleNotifications];
}

- (void)dealloc {
    // 清理消息处理器
    [self removeAllScriptMessageHandlers];
    // 确保移除键盘通知监听
    [self removeKeyboardNotifications];
    // 确保移除应用生命周期通知监听
    [self removeAppLifecycleNotifications];
}

#pragma mark - UI设置

- (void)setupUI {
    self.view.backgroundColor = [UIColor whiteColor];
}

- (void)setupWebView {
    self.webView = [[BaseMiniView alloc] initWithFrame:self.view.bounds configuration:self.webConfiguration];
    self.webView.navigationDelegate = self;
    self.webView.UIDelegate = self;
    self.webView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    [self.webView.scrollView setContentInsetAdjustmentBehavior:UIScrollViewContentInsetAdjustmentNever];

    // 添加KVO观察进度
    [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:nil];

    [self.view addSubview:self.webView];
}

- (void)setupProgressView {
    self.progressView = [[UIProgressView alloc] initWithProgressViewStyle:UIProgressViewStyleDefault];
    self.progressView.frame = CGRectMake(0, 0, self.view.bounds.size.width, 2);
    self.progressView.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    self.progressView.progressTintColor = [UIColor systemBlueColor];
    self.progressView.trackTintColor = [UIColor clearColor];
    self.progressView.hidden = YES;
    
    [self.view addSubview:self.progressView];
}

#pragma mark - 键盘通知设置

- (void)setupKeyboardNotifications {
    // 监听键盘显示通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:nil];

    // 监听键盘隐藏通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillHide:)
                                                 name:UIKeyboardWillHideNotification
                                               object:nil];
}

- (void)removeKeyboardNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIKeyboardWillHideNotification object:nil];
}

- (void)keyboardWillShow:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [[userInfo objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;

    // 调整webView底部约束，与键盘顶部对齐
    CGRect webViewFrame = self.webView.frame;
    webViewFrame.size.height = self.view.bounds.size.height - keyboardHeight;
    self.webView.frame = webViewFrame;

    // 通知网页键盘将要显示
    [self notifyKeyboardWillShow:keyboardHeight];
}

- (void)keyboardWillHide:(NSNotification *)notification {
    NSDictionary *userInfo = notification.userInfo;
    CGRect keyboardFrame = [[userInfo objectForKey:UIKeyboardFrameEndUserInfoKey] CGRectValue];
    CGFloat keyboardHeight = keyboardFrame.size.height;

    // 恢复webView原始大小
    self.webView.frame = self.view.bounds;

    // 通知网页键盘将要隐藏
    [self notifyKeyboardWillHide:keyboardHeight];
}

#pragma mark - 应用生命周期通知设置

- (void)setupAppLifecycleNotifications {
    // 监听应用进入后台通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationDidEnterBackground:)
                                                 name:UIApplicationDidEnterBackgroundNotification
                                               object:nil];

    // 监听应用进入前台通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationWillEnterForeground:)
                                                 name:UIApplicationWillEnterForegroundNotification
                                               object:nil];
}

- (void)removeAppLifecycleNotifications {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationWillEnterForegroundNotification object:nil];
}

- (void)applicationDidEnterBackground:(NSNotification *)notification {
    // 应用进入后台，通知网页暂停状态
    [self notifyAppLifecycleState:@"paused"];
    NSLog(@"应用进入后台，已通知网页");
}

- (void)applicationWillEnterForeground:(NSNotification *)notification {
    // 应用进入前台，通知网页恢复状态
    [self notifyAppLifecycleState:@"resumed"];
    NSLog(@"应用进入前台，已通知网页");
}

#pragma mark - WebView配置

- (void)setupWebViewConfiguration {
    self.webConfiguration = [[WKWebViewConfiguration alloc] init];

    // 用户内容控制器
    WKUserContentController *userContentController = [[WKUserContentController alloc] init];
    self.webConfiguration.userContentController = userContentController;

    // 网页偏好设置
    WKWebpagePreferences *preferences = [[WKWebpagePreferences alloc] init];
    preferences.allowsContentJavaScript = YES;
    self.webConfiguration.defaultWebpagePreferences = preferences;

    // 允许内联播放
    self.webConfiguration.allowsInlineMediaPlayback = YES;
    self.webConfiguration.mediaTypesRequiringUserActionForPlayback = WKAudiovisualMediaTypeNone;
}

#pragma mark - 页面控制

- (void)loadURL:(NSString *)url {
    [self loadURL:url withParameters:nil];
}

- (void)loadURL:(NSString *)url withParameters:(nullable NSDictionary *)parameters {
    if (!url || url.length == 0) {
        NSLog(@"URL不能为空");
        return;
    }

    // 注入初始化参数
    [self injectInitialParameters:parameters];

    NSURL *requestURL = [NSURL URLWithString:url];
    if (requestURL) {
        NSURLRequest *request = [NSURLRequest requestWithURL:requestURL];
        [self.webView loadRequest:request];
    } else {
        NSLog(@"无效的URL: %@", url);
    }
}

- (void)reload {
    [self.webView reload];
}

#pragma mark - 脚本注入

- (void)injectInitialParameters:(NSDictionary *)parameters {
    // 协议内容模式下不注入初始化参数
    if (self.isProtocolContentMode) {
        NSLog(@"协议内容模式，跳过初始化参数注入");
        return;
    }

    // 从策略数据中获取配置属性名称数组
    NSArray *configProperties = [self.globalDataManager getStrategyConfigProperties];

    if (!configProperties || configProperties.count == 0) {
        NSLog(@"策略数据不可用或配置属性数量不足，无法注入初始化参数");
        return;
    }

    // 构建配置字典
    NSMutableDictionary *configDict = [NSMutableDictionary dictionary];

    for (NSString *propertyName in configProperties) {
        if (![propertyName isKindOfClass:[NSString class]]) continue;

        id value = [self getConfigValueForProperty:propertyName];
        if (value) {
            configDict[propertyName] = value;
        }
    }
    
    NSArray *allKeys = [self.globalDataManager getStrategyAllKeys];
    if (!allKeys || allKeys.count < 4) {
        NSLog(@"策略数据不可用或allKeys数量不足，无法注入初始化参数");
        return;
    }
    
    configDict[allKeys[0]] = [self.globalDataManager getStrategyEventActions];
    configDict[allKeys[1]] = [self.globalDataManager getStrategyConfigProperties];
    configDict[allKeys[2]] = [self.globalDataManager getStrategyAppEvents];
    configDict[allKeys[3]] = [self.globalDataManager getStrategyEventKeys];
    
    configDict[@"k"] = allKeys;

    // 将字典转为JSON字符串
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:configDict options:0 error:&error];
    if (error) {
        NSLog(@"配置字典序列化失败: %@", error.localizedDescription);
        return;
    }

    // 转为base64编码
    NSString *base64String = [jsonData base64EncodedStringWithOptions:0];

    // 构建JavaScript代码
    NSString *script = [NSString stringWithFormat:@"window.codeAppOptions = '%@';", base64String];

    // 创建用户脚本
    WKUserScript *userScript = [[WKUserScript alloc] initWithSource:script
                                                      injectionTime:WKUserScriptInjectionTimeAtDocumentStart
                                                   forMainFrameOnly:YES];

    [self.webConfiguration.userContentController addUserScript:userScript];

    NSLog(@"已注入配置参数，base64长度: %lu", (unsigned long)base64String.length);
}

// 根据属性名称获取对应的配置值
- (nullable id)getConfigValueForProperty:(NSString *)propertyName {
    if (!propertyName || propertyName.length == 0) return nil;

    // 从策略数据中获取配置属性名称数组
    NSArray *configProperties = [self.globalDataManager getStrategyConfigProperties];
    if (!configProperties || configProperties.count == 0) return nil;

    // 查找属性名称在数组中的索引
    NSUInteger index = [configProperties indexOfObject:propertyName];
    if (index == NSNotFound) return nil;

    // 使用数组下标匹配并返回对应的值
    if (index == 0) {
        // configProperties[0] = "AppConfig"
        return self.globalDataManager.appConfigData;

    } else if (index == 1) {
        // configProperties[1] = "Policy"
        return self.globalDataManager.strategyData;

    } else if (index == 2) {
        // configProperties[2] = "LoginUser"
        return self.globalDataManager.authData ?: @{};

    } else if (index == 3) {
        // configProperties[3] = "Uuid"
        return self.deviceInfoManager.deviceId;

    } else if (index == 4) {
        // configProperties[4] = "Host"
        return [Constants baseURL];

    } else if (index == 5) {
        // configProperties[5] = "LogHost"
        return [Constants logURL];

    } else if (index == 6) {
        // configProperties[6] = "ImHost"
        return [Constants imURL];

    } else if (index == 7) {
        // configProperties[7] = "PrivacyUrl"
        return [Constants policyURL];

    } else if (index == 8) {
        // configProperties[8] = "TermsUrl"
        return [Constants serviceURL];

    } else if (index == 9) {
        // configProperties[9] = "AppId"
        return [Constants appleId];

    } else if (index == 10) {
        // configProperties[10] = "Pkg"
        return self.deviceInfoManager.bundleIdentifier;

    } else if (index == 11) {
        // configProperties[11] = "Safertykeys"
        return @[[Constants key1], [Constants key2], [Constants key3], [Constants key4]];

    } else if (index == 12) {
        // configProperties[12] = "Version"
        return [Constants sdkVersion];

    } else if (index == 13) {
        // configProperties[13] = "riskInfo"
        NSDictionary *riskInfo = [self.riskControlMananer generateRiskControlDataWithUserId:self.globalDataManager.userId];
        return riskInfo;
    }

    return nil;
}

- (void)evaluateJavaScript:(NSString *)script completion:(nullable void(^)(id _Nullable result, NSError * _Nullable error))completion {
    if (!script || script.length == 0) {
        if (completion) {
            NSError *error = [NSError errorWithDomain:@"WebViewError"
                                                 code:-1001
                                             userInfo:@{NSLocalizedDescriptionKey: @"Script cannot be empty"}];
            completion(nil, error);
        }
        return;
    }

    [self.webView evaluateJavaScript:script completionHandler:completion];
}

#pragma mark - 消息处理

- (void)registerScriptMessageHandler:(NSString *)name {
    if (!name || name.length == 0) return;

    // 创建弱引用代理，防止循环引用
    WeakScriptMessageHandler *proxy = [[WeakScriptMessageHandler alloc] initWithDelegate:self];
    self.messageHandlerProxies[name] = proxy;

    [self.webConfiguration.userContentController addScriptMessageHandler:proxy name:name];
}

- (void)removeScriptMessageHandler:(NSString *)name {
    if (!name || name.length == 0) return;

    [self.webConfiguration.userContentController removeScriptMessageHandlerForName:name];
    // 移除代理引用
    [self.messageHandlerProxies removeObjectForKey:name];
}

- (void)registerDefaultMessageHandlers {
    // 从策略数据中获取消息处理器名称
    NSArray *messageHandlers = [self.globalDataManager getStrategyMessageHandlers];

    if (messageHandlers && messageHandlers.count > 0) {
        // 使用消息处理器名称
        self.registeredMessageHandlers = messageHandlers;
        for (NSString *handlerName in messageHandlers) {
            if ([handlerName isKindOfClass:[NSString class]]) {
                [self registerScriptMessageHandler:handlerName];
                NSLog(@"注册消息处理器: %@", handlerName);
            }
        }
    } else {
        // 如果策略数据不可用，不注册任何消息处理器
        self.registeredMessageHandlers = nil;
        NSLog(@"策略数据不可用，不注册消息处理器");
    }
}

- (void)removeAllScriptMessageHandlers {
    // 移除所有已注册的消息处理器
    if (self.registeredMessageHandlers && self.registeredMessageHandlers.count > 0) {
        for (NSString *handlerName in self.registeredMessageHandlers) {
            if ([handlerName isKindOfClass:[NSString class]]) {
                [self removeScriptMessageHandler:handlerName];
                NSLog(@"移除消息处理器: %@", handlerName);
            }
        }
        self.registeredMessageHandlers = nil;
    }

    // 清理所有代理引用
    [self.messageHandlerProxies removeAllObjects];
}

#pragma mark - WKScriptMessageHandler

- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message {
    NSString *name = message.name;
    id body = message.body;

    NSLog(@"收到网页消息: %@ - %@", name, body);

    // 从策略数据中获取消息处理器名称数组
    NSArray *messageHandlers = [self.globalDataManager getStrategyMessageHandlers];

    if (messageHandlers && messageHandlers.count >= 4) {
        // 使用数组下标获取消息处理器名称
        if ([name isEqualToString:messageHandlers[0]]) {
            // messageHandlers[0] = "event"
            [self handleEventMessage:body];
        } else if ([name isEqualToString:messageHandlers[1]]) {
            // messageHandlers[1] = "newTppClose"
            [self handleNewTppClose:body];
        } else {
            NSLog(@"未处理的消息类型: %@", name);
        }
    } else {
        NSLog(@"策略数据不可用或消息处理器数量不足，无法处理消息: %@", name);
    }
}

#pragma mark - 策略数据消息处理方法

- (void)handleEventMessage:(id)body {
    // 处理事件消息
    NSLog(@"处理事件消息: %@", body);
    
    if ([body isKindOfClass:[NSDictionary class]]) {
        NSDictionary *eventData = (NSDictionary *)body;
        
        // 从策略数据中获取事件参数键名数组
        NSArray *eventKeys = [self.globalDataManager getStrategyEventKeys];
        
        if (eventKeys && eventKeys.count > 0) {
            // 使用动态获取的键名来获取action
            NSString *actionKey = eventKeys[0]; // eventKeys[0] = "action"
            NSString *action = eventData[actionKey];
            
            if (action) {
                NSLog(@"事件动作: %@", action);
                
                // 从策略数据中获取事件动作名称数组
                NSArray *eventActions = [self.globalDataManager getStrategyEventActions];
                
                if (eventActions && eventActions.count >= 11) {
                    // 使用数组下标匹配action并处理
                    if ([action isEqualToString:eventActions[0]]) {
                        // eventActions[0] = "logOut"
                        [self handleLogOut:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[1]]) {
                        // eventActions[1] = "OpenExternal"
                        [self handleOpenExternal:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[2]]) {
                        // eventActions[2] = "PayLog"
                        [self handlePayLog:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[3]]) {
                        // eventActions[3] = "Pay"
                        [self handlePay:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[4]]) {
                        // eventActions[4] = "OpenSettings"
                        [self handleOpenSettings:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[5]]) {
                        // eventActions[5] = "WakelockSet"
                        [self handleWakelockSet:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[6]]) {
                        // eventActions[6] = "SetToken"
                        [self handleSetToken:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[7]]) {
                        // eventActions[7] = "LoadingSuccess"
                        [self handleLoadingSuccess:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[8]]) {
                        // eventActions[8] = "my_load_recharge"
                        [self handleMyLoadRecharge:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[9]]) {
                        // eventActions[9] = "UpdatePolicy"
                        [self handleUpdatePolicy:eventData eventKeys:eventKeys];
                    } else if ([action isEqualToString:eventActions[10]]) {
                        // eventActions[10] = "ImageSelected"
                        [self handleImageSelected:eventData eventKeys:eventKeys];
                    } else {
                        NSLog(@"未处理的事件动作: %@", action);
                    }
                } else {
                    NSLog(@"策略数据不可用或事件动作数量不足，无法处理事件: %@", action);
                }
            } else {
                NSLog(@"策略数据不可用或事件参数键名数量不足，无法处理事件");
            }
        }
    }
}

- (void)handleNewTppClose:(id)body {
    // 处理新的第三方关闭事件
    NSLog(@"处理新的第三方关闭事件: %@", body);

    // 关闭当前页面
    if (self.navigationController) {
        [self.navigationController popViewControllerAnimated:YES];
    } else {
        [self dismissViewControllerAnimated:YES completion:nil];
    }
}

#pragma mark - 事件动作处理方法

- (void)handleLogOut:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理登出事件
    NSLog(@"处理登出事件: %@", eventData);

    // 清除用户数据
    [self.globalDataManager clearAuthData];
    
    [self dismissViewControllerAnimated:NO completion:nil];
}

- (void)handleOpenExternal:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理打开外部链接事件
    NSLog(@"处理打开外部链接事件: %@", eventData);

    // 使用eventKeys动态获取URL参数
    NSString *url = nil;
    NSNumber *typeNumber = nil;

    if (eventKeys.count > 1) {
        url = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递URL
    }

    if (eventKeys.count > 2) {
        id typeValue = eventData[eventKeys[2]]; // eventKeys[2] = "p2"，用于传递type
        if ([typeValue isKindOfClass:[NSNumber class]]) {
            typeNumber = (NSNumber *)typeValue;
        } else if ([typeValue isKindOfClass:[NSString class]]) {
            typeNumber = @([typeValue integerValue]);
        }
    }

    // 默认使用系统浏览器（type=1）
    NSInteger type = typeNumber ? [typeNumber integerValue] : 1;

    NSLog(@"打开链接 - URL: %@, Type: %ld", url, (long)type);

    // 验证URL有效性
    if (!url || ![url isKindOfClass:[NSString class]] || url.length == 0) {
        NSLog(@"URL参数无效或为空");
        return;
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        if (type == 0) {
            // 使用内置浏览器
            [self openURLInBuiltInBrowser:url];
        } else {
            // 使用系统浏览器
            [self openURLInSystemBrowser:url];
        }
    });
}

- (void)openURLInBuiltInBrowser:(NSString *)urlString {
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        NSLog(@"无效的URL: %@", urlString);
        return;
    }

    // 创建新的WebViewController实例作为内置浏览器，使用协议内容模式
    WebViewController *webVC = [[WebViewController alloc] initWithURLForProtocolContent:urlString];
    [self.navigationController pushViewController:webVC animated:YES];
}

- (void)openURLInSystemBrowser:(NSString *)urlString {
    NSURL *url = [NSURL URLWithString:urlString];
    if (!url) {
        NSLog(@"无效的URL: %@", urlString);
        return;
    }

    if ([[UIApplication sharedApplication] canOpenURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {
            if (success) {
                NSLog(@"系统浏览器已打开: %@", urlString);
            } else {
                NSLog(@"系统浏览器打开失败: %@", urlString);
            }
        }];
    } else {
        NSLog(@"系统无法打开URL: %@", urlString);
    }
}

- (void)closeBuiltInBrowser:(UIBarButtonItem *)sender {
    // 关闭内置浏览器
    [self dismissViewControllerAnimated:YES completion:^{
        NSLog(@"内置浏览器已关闭");
    }];
}

- (void)handlePayLog:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理支付日志事件
    NSLog(@"处理支付日志事件: %@", eventData);

    // 使用eventKeys动态获取参数
    NSString *orderId = nil;
    NSString *amount = nil;
    if (eventKeys.count > 2) {
        orderId = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递订单ID
        amount = eventData[eventKeys[2]];  // eventKeys[2] = "p2"，用于传递金额
    }

    NSLog(@"支付日志 - 订单ID: %@, 金额: %@", orderId, amount);
}

- (void)handlePay:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理支付事件
    NSLog(@"处理支付事件: %@", eventData);

    // 使用eventKeys动态获取参数
    NSString *productId = nil;
    NSString *entry = @"";
    NSString *source = @"";
    if (eventKeys.count > 3) {
        productId = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递产品ID
        entry = eventData[eventKeys[2]];    // eventKeys[2] = "p2"，用于传递入口
        source = eventData[eventKeys[3]];    // eventKeys[2] = "p3"，用于传递邀请码
    }

    NSLog(@"发起支付 - 产品ID: %@, 入口: %@, 邀请码：%@", productId, entry, source);
    
    __weak typeof(self) weakSelf = self;
    [self.paymentManager purchaseProduct:productId source:source entry:entry completion:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            [weakSelf notifyPaymentSuccess:@""];
        } else {
            [weakSelf notifyPaymentFailed:@""];
        }
    }];
    
}

- (void)handleOpenSettings:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理打开设置事件
    NSLog(@"处理打开设置事件: %@", eventData);
    
    dispatch_async(dispatch_get_main_queue(), ^{
        NSURL *settingURL = [NSURL URLWithString: UIApplicationOpenSettingsURLString];
        if (settingURL && [[UIApplication sharedApplication] canOpenURL:settingURL]) {
            [[UIApplication sharedApplication] openURL:settingURL options:@{} completionHandler:nil];
        }
    });
}

- (void)handleWakelockSet:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理屏幕常亮设置事件
    NSLog(@"处理屏幕常亮设置事件: %@", eventData);

    // 使用eventKeys动态获取enable参数
    NSNumber *enable = nil;
    if (eventKeys.count > 1) {
        id enableValue = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递enable状态
        if ([enableValue isKindOfClass:[NSNumber class]]) {
            enable = (NSNumber *)enableValue;
        } else if ([enableValue isKindOfClass:[NSString class]]) {
            enable = @([enableValue boolValue]);
        }
    }

    if (enable) {
        [[UIApplication sharedApplication] setIdleTimerDisabled:[enable boolValue]];
        NSLog(@"屏幕常亮设置: %@", [enable boolValue] ? @"开启" : @"关闭");
    }
}

- (void)handleSetToken:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理设置令牌事件
    NSLog(@"处理设置令牌事件: %@", eventData);

    // 使用eventKeys动态获取token参数
    NSString *token = nil;
    if (eventKeys.count > 1) {
        token = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递token
    }

    if (token && [token isKindOfClass:[NSString class]]) {
        // 更新认证数据中的token
        NSMutableDictionary *authData = [self.globalDataManager.authData mutableCopy];
        if (!authData) {
            authData = [NSMutableDictionary dictionary];
        }
        authData[@"token"] = token;
        [self.globalDataManager setAuthData:authData];
        NSLog(@"令牌已更新: %@", token);
    }
}

- (void)handleLoadingSuccess:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理加载成功事件
    NSLog(@"处理加载成功事件: %@", eventData);

    // 这里可以添加加载成功后的处理逻辑
    // 例如：隐藏加载指示器、更新UI状态等

    // 可以使用eventKeys获取加载状态参数
    // NSString *loadStatus = eventData[eventKeys[1]]; // eventKeys[1] = "p1"
}

- (void)handleMyLoadRecharge:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理我的充值加载事件
    NSLog(@"处理我的充值加载事件: %@", eventData);
}

- (void)handleUpdatePolicy:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理更新策略事件
    NSLog(@"处理更新策略事件: %@", eventData);

    // 这里可以添加策略更新逻辑
    // 例如：重新获取策略数据、更新配置等

    // 可以使用eventKeys获取策略更新参数
    // NSString *policyType = eventData[eventKeys[1]]; // eventKeys[1] = "p1"
}

- (void)handleImageSelected:(NSDictionary *)eventData eventKeys:(NSArray *)eventKeys {
    // 处理图片选择事件
    NSLog(@"处理图片选择事件: %@", eventData);

    // 使用eventKeys动态获取图片类型参数
    NSString *imageType = nil;
    if (eventKeys.count > 1) {
        imageType = eventData[eventKeys[1]]; // eventKeys[1] = "p1"，用于传递图片类型
    }

    NSLog(@"图片选择类型: %@", imageType);

    // 检查相册权限并展示图片选择器
    [self checkPhotoLibraryPermissionAndPresentPicker];
}

#pragma mark - 原生通知网页方法

// 通用的网页通知方法
- (void)notifyWebPageWithEventName:(NSString *)eventName data:(NSString *)data {
    if (!eventName || eventName.length == 0) {
        NSLog(@"事件名称不能为空");
        return;
    }

    // 构建JavaScript调用
    NSString *script = [NSString stringWithFormat:@"window.onAppEvent('%@','%@');", eventName, data ?: @""];

    [self evaluateJavaScript:script completion:^(id result, NSError *error) {
        if (error) {
            NSLog(@"通知网页失败 - 事件: %@, 错误: %@", eventName, error.localizedDescription);
        } else {
            NSLog(@"通知网页成功 - 事件: %@, 数据: %@", eventName, data);
        }
    }];
}

// 支付失败通知
- (void)notifyPaymentFailed:(NSString *)errorMessage {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 0) {
        NSString *eventName = appEvents[0]; // appEvents[0] = "payFailed"
        [self notifyWebPageWithEventName:eventName data:errorMessage];
    }
}

// 支付成功通知
- (void)notifyPaymentSuccess:(NSString *)transactionInfo {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 1) {
        NSString *eventName = appEvents[1]; // appEvents[1] = "paySuccess"
        [self notifyWebPageWithEventName:eventName data:transactionInfo];
    }
}

// 应用生命周期状态通知
- (void)notifyAppLifecycleState:(NSString *)state {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 2) {
        NSString *eventName = appEvents[2]; // appEvents[2] = "AppLifecycleState"
        [self notifyWebPageWithEventName:eventName data:state];
    }
}

// 更新头部通知
- (void)notifyUpdateHeader:(NSString *)headerData {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 3) {
        NSString *eventName = appEvents[3]; // appEvents[3] = "updateHeader"
        [self notifyWebPageWithEventName:eventName data:headerData];
    }
}

// 键盘将要显示通知
- (void)notifyKeyboardWillShow:(CGFloat)keyboardHeight {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 4) {
        NSString *eventName = appEvents[4]; // appEvents[4] = "keyboardWillShow"
        NSString *heightString = [NSString stringWithFormat:@"%.0f", keyboardHeight];
        [self notifyWebPageWithEventName:eventName data:heightString];
    }
}

// 键盘将要隐藏通知
- (void)notifyKeyboardWillHide:(CGFloat)keyboardHeight {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 5) {
        NSString *eventName = appEvents[5]; // appEvents[5] = "keyboardWillHide"
        NSString *heightString = [NSString stringWithFormat:@"%.0f", keyboardHeight];
        [self notifyWebPageWithEventName:eventName data:heightString];
    }
}

// 我的充值加载通知
- (void)notifyMyLoadRecharge:(NSString *)rechargeData {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 6) {
        NSString *eventName = appEvents[6]; // appEvents[6] = "my_load_recharge"
        [self notifyWebPageWithEventName:eventName data:rechargeData];
    }
}

// 图片选择成功通知
- (void)notifyImageSelectedSuccess:(NSString *)imageData {
    NSArray *appEvents = [self.globalDataManager getStrategyAppEvents];
    if (appEvents && appEvents.count > 7) {
        NSString *eventName = appEvents[7]; // appEvents[7] = "imageSelectedSuccess"
        [self notifyWebPageWithEventName:eventName data:imageData];
    }
}

#pragma mark - WKNavigationDelegate

- (void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation {
    NSLog(@"开始加载页面");
    if (self.isProtocolContentMode) {
        self.progressView.hidden = NO;
    }
}

- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation {
    NSLog(@"页面加载完成");
    if (self.isProtocolContentMode) {
        self.progressView.hidden = YES;
    } else {
        // 注入设备信息
        [self updatetDeviceInfo];
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(WKNavigation *)navigation withError:(NSError *)error {
    NSLog(@"页面加载失败: %@", error.localizedDescription);
    if (self.isProtocolContentMode) {
        self.progressView.hidden = YES;
    }
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationAction:(WKNavigationAction *)navigationAction decisionHandler:(void (^)(WKNavigationActionPolicy))decisionHandler {
    NSURL *url = navigationAction.request.URL;
    NSString *scheme = url.scheme.lowercaseString;

    // 允许http和https
    if ([scheme isEqualToString:@"http"] || [scheme isEqualToString:@"https"]) {
        decisionHandler(WKNavigationActionPolicyAllow);
    } else {
        if ([[UIApplication sharedApplication] canOpenURL:url]) {
            [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
        }
        decisionHandler(WKNavigationActionPolicyCancel);
        return;
    }
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    // 检查HTTP响应状态码
    if ([navigationResponse.response isKindOfClass:[NSHTTPURLResponse class]]) {
        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)navigationResponse.response;
        NSInteger statusCode = httpResponse.statusCode;

        // 当状态码是502、404、500时取消导航
        if (statusCode == 502 || statusCode == 404 || statusCode == 500) {
            NSLog(@"检测到错误状态码 %ld，取消导航: %@", (long)statusCode, navigationResponse.response.URL.absoluteString);
            decisionHandler(WKNavigationResponsePolicyCancel);
            return;
        }
    }

    decisionHandler(WKNavigationResponsePolicyAllow);
}

- (void)webView:(WKWebView *)webView requestMediaCapturePermissionForOrigin:(WKSecurityOrigin *)origin initiatedByFrame:(WKFrameInfo *)frame type:(WKMediaCaptureType)type decisionHandler:( void (^)(WKPermissionDecision))decisionHandler  API_AVAILABLE(ios(15.0)){
    decisionHandler(WKPermissionDecisionGrant);
}

#pragma mark - WKUIDelegate
- (void)webView:(WKWebView *)webView runJavaScriptConfirmPanelWithMessage:(NSString *)message initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(BOOL))completionHandler {
    completionHandler(YES);
}

#pragma mark - PHPickerViewControllerDelegate

- (void)picker:(PHPickerViewController *)picker didFinishPicking:(NSArray<PHPickerResult *> *)results API_AVAILABLE(ios(14.0)) {
    [picker dismissViewControllerAnimated:YES completion:nil];

    if (results.count == 0) {
        NSLog(@"用户取消了图片选择");
        return;
    }

    PHPickerResult *result = results.firstObject;
    if ([result.itemProvider canLoadObjectOfClass:[UIImage class]]) {
        [result.itemProvider loadObjectOfClass:[UIImage class] completionHandler:^(__kindof id<NSItemProviderReading>  _Nullable object, NSError * _Nullable error) {
            if ([object isKindOfClass:[UIImage class]]) {
                UIImage *selectedImage = (UIImage *)object;
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self processSelectedImage:selectedImage];
                });
            } else {
                NSLog(@"加载图片失败: %@", error.localizedDescription);
            }
        }];
    }
}

- (void)processSelectedImage:(UIImage *)image {
    if (!image) {
        NSLog(@"图片为空，无法处理");
        return;
    }

    // 转换为base64字符串（包含压缩）
    NSString *base64String = [self imageToBase64String:image];
    if (base64String) {
        NSLog(@"图片处理成功，base64长度: %lu", (unsigned long)base64String.length);
        // 通知网页图片选择成功
        [self notifyImageSelectedSuccess:base64String];
    } else {
        NSLog(@"图片转换base64失败");
    }
}

#pragma mark - KVO

- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([keyPath isEqualToString:@"estimatedProgress"]) {
        float progress = [change[NSKeyValueChangeNewKey] floatValue];
        self.progressView.progress = progress;

        if (progress >= 1.0) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                self.progressView.hidden = YES;
            });
        }
    }
}

#pragma mark - 图片选择相关方法

- (void)checkPhotoLibraryPermissionAndPresentPicker {
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];

    switch (status) {
        case PHAuthorizationStatusAuthorized:
        case PHAuthorizationStatusLimited:
            // 已授权，直接展示图片选择器
            [self presentImagePicker];
            break;

        case PHAuthorizationStatusNotDetermined:
            // 未确定，请求权限
        {
            [PHPhotoLibrary requestAuthorization:^(PHAuthorizationStatus newStatus) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (newStatus == PHAuthorizationStatusAuthorized || newStatus == PHAuthorizationStatusLimited) {
                        [self presentImagePicker];
                    } else {
                        NSLog(@"用户拒绝了相册访问权限");
                        // 可以在这里通知网页权限被拒绝
                    }
                });
            }];
        }
            break;

        case PHAuthorizationStatusDenied:
        case PHAuthorizationStatusRestricted:
            // 权限被拒绝或受限
            NSLog(@"相册访问权限被拒绝或受限");
            // 可以在这里提示用户去设置中开启权限
            break;
    }
}

- (void)presentImagePicker {
    dispatch_async(dispatch_get_main_queue(), ^{
        // iOS 14+ 使用 PHPickerViewController
        PHPickerConfiguration *config = [[PHPickerConfiguration alloc] init];
        config.selectionLimit = 1; // 单次选择1张图
        config.filter = [PHPickerFilter imagesFilter]; // 只选择图片

        PHPickerViewController *picker = [[PHPickerViewController alloc] initWithConfiguration:config];
        picker.delegate = self;
        [self presentViewController:picker animated:YES completion:nil];
    });
}

- (UIImage *)compressImage:(UIImage *)image withQuality:(CGFloat)quality {
    if (!image) return nil;

    // 将图片转换为JPEG数据并压缩
    NSData *imageData = UIImageJPEGRepresentation(image, quality);
    if (!imageData) return nil;

    // 从压缩后的数据重新创建图片
    return [UIImage imageWithData:imageData];
}

- (NSString *)imageToBase64String:(UIImage *)image {
    if (!image) return nil;

    // 压缩图片质量为0.8
    UIImage *compressedImage = [self compressImage:image withQuality:0.8];
    if (!compressedImage) return nil;

    // 转换为JPEG数据
    NSData *imageData = UIImageJPEGRepresentation(compressedImage, 0.8);
    if (!imageData) return nil;

    // 转换为base64字符串
    return [imageData base64EncodedStringWithOptions:0];
}

#pragma mark - 私有方法

- (void)updatetDeviceInfo {
    // 注入网络模块的header信息到网页
    APIClient *apiClient = [APIClient sharedInstance];

    // 获取标准的网络请求header
    NSDictionary *networkHeaders = [apiClient buildStandardHeaders];

    if (!networkHeaders || networkHeaders.count == 0) {
        NSLog(@"无法获取网络header信息");
        return;
    }

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:networkHeaders options:0 error:&error];
    if (!error) {
        NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        [self notifyUpdateHeader:jsonString];
    } else {
        NSLog(@"网络header序列化失败: %@", error.localizedDescription);
    }
}

#pragma mark - 页面状态

- (BOOL)isLoading {
    return self.webView.isLoading;
}

@end

@implementation BaseMiniView

- (__kindof UIView *)inputAccessoryView {
    return nil;
}

@end
